import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for updating a return
const updateReturnSchema = z.object({
  reason: z.string().min(1).optional(),
  notes: z.string().optional(),
  status: z.enum(['PENDING', 'APPROVED', 'COMPLETED', 'REJECTED']).optional(),
});

// GET /api/returns/[id] - Get specific return details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;
    const returnRecord = await prisma.return.findUnique({
      where: { id },
      include: {
        customer: true,
        transaction: {
          include: {
            cashier: {
              select: { id: true, name: true, email: true },
            },
          },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    if (!returnRecord) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    return NextResponse.json(returnRecord);
  } catch (error) {
    console.error('Error fetching return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/returns/[id] - Update return status/notes
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = updateReturnSchema.parse(body);

    // Check if return exists
    const existingReturn = await prisma.return.findUnique({
      where: { id },
    });

    if (!existingReturn) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    // Prevent updating completed or rejected returns
    if (existingReturn.status === 'COMPLETED' || existingReturn.status === 'REJECTED') {
      return NextResponse.json({ 
        error: 'Cannot update completed or rejected returns' 
      }, { status: 400 });
    }

    const updatedReturn = await prisma.return.update({
      where: { id },
      data: validatedData,
      include: {
        customer: true,
        transaction: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    return NextResponse.json(updatedReturn);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error updating return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/returns/[id] - Cancel pending return
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;
    // Check if return exists and is pending
    const existingReturn = await prisma.return.findUnique({
      where: { id },
    });

    if (!existingReturn) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    if (existingReturn.status !== 'PENDING') {
      return NextResponse.json({ 
        error: 'Can only cancel pending returns' 
      }, { status: 400 });
    }

    await prisma.return.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Return cancelled successfully' });
  } catch (error) {
    console.error('Error cancelling return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
